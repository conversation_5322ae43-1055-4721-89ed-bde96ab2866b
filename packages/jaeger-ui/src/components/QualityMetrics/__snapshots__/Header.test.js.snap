// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Header rendering renders as expected with full props 1`] = `
<header
  className="QualityMetrics--Header"
>
  <NameSelector
    label="Service"
    options={
      Array [
        "foo",
        "bar",
        "baz",
      ]
    }
    placeholder="Select a service…"
    required={true}
    setValue={[MockFunction]}
    value="test service"
  />
  <label
    className="QualityMetrics--Header--LookbackLabel"
    htmlFor="inputNumber"
  >
    Lookback:
  </label>
  <InputNumber
    id="inputNumber"
    min={1}
    onChange={[Function]}
    value={4}
  />
  <span
    className="QualityMetrics--Header--LookbackSuffix"
  >
    (in hours)
  </span>
</header>
`;

exports[`Header rendering renders as expected with minimum props 1`] = `
<header
  className="QualityMetrics--Header"
>
  <NameSelector
    label="Service"
    options={Array []}
    placeholder="Select a service…"
    required={true}
    setValue={[MockFunction]}
    value={null}
  />
  <label
    className="QualityMetrics--Header--LookbackLabel"
    htmlFor="inputNumber"
  >
    Lookback:
  </label>
  <InputNumber
    id="inputNumber"
    min={1}
    onChange={[Function]}
    value={4}
  />
  <span
    className="QualityMetrics--Header--LookbackSuffix"
  >
    (in hours)
  </span>
</header>
`;
