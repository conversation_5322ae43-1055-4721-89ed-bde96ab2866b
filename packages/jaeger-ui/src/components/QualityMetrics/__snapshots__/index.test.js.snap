// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`QualityMetrics UnconnectedQualityMetrics render renders when errored 1`] = `
<div
  className="QualityMetrics"
>
  <Header
    lookback={48}
    service="test-service"
    services={
      Array [
        "foo",
        "bar",
        "baz",
      ]
    }
    setLookback={[Function]}
    setService={[Function]}
  />
  <div
    className="QualityMetrics--Error"
  >
    Error message
  </div>
</div>
`;

exports[`QualityMetrics UnconnectedQualityMetrics render renders when loading 1`] = `
<div
  className="QualityMetrics"
>
  <Header
    lookback={48}
    service="test-service"
    services={
      Array [
        "foo",
        "bar",
        "baz",
      ]
    }
    setLookback={[Function]}
    setService={[Function]}
  />
  <LoadingIndicator
    centered={true}
  />
</div>
`;

exports[`QualityMetrics UnconnectedQualityMetrics render renders with metrics 1`] = `
<div
  className="QualityMetrics"
>
  <Header
    lookback={48}
    service="test-service"
    services={
      Array [
        "foo",
        "bar",
        "baz",
      ]
    }
    setLookback={[Function]}
    setService={[Function]}
  />
  <Memo(BannerText)
    bannerText="test banner text"
  />
  <div
    className="QualityMetrics--Body"
  >
    <div
      className="QualityMetrics--ScoreCards"
    >
      <Memo(ScoreCard)
        key="score0"
        link="trace.quality.documentation/link"
        score={
          Object {
            "key": "score0",
          }
        }
      />
      <Memo(ScoreCard)
        key="score1"
        link="trace.quality.documentation/link"
        score={
          Object {
            "key": "score1",
          }
        }
      />
    </div>
    <div
      className="QualityMetrics--MetricCards"
    >
      <Memo(MetricCard)
        key="metric 0"
        metric={
          Object {
            "name": "metric 0",
          }
        }
      />
      <Memo(MetricCard)
        key="metric 1"
        metric={
          Object {
            "name": "metric 1",
          }
        }
      />
    </div>
  </div>
</div>
`;

exports[`QualityMetrics UnconnectedQualityMetrics render renders without loading, error, or metrics 1`] = `
<div
  className="QualityMetrics"
>
  <Header
    lookback={48}
    services={
      Array [
        "foo",
        "bar",
        "baz",
      ]
    }
    setLookback={[Function]}
    setService={[Function]}
  />
</div>
`;
