// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<OperationTableDetails> "Couldn’t fetch data" displayed 1`] = `
<div
  className="ops-table-error-placeholder"
>
  Couldn’t fetch data
</div>
`;

exports[`<OperationTableDetails> Loading indicator is displayed 1`] = `
<LoadingIndicator
  centered={true}
/>
`;

exports[`<OperationTableDetails> Table rendered successfully 1`] = `
<Col
  span={24}
>
  <Table
    columns={
      Array [
        Object {
          "className": "header-item",
          "dataIndex": "name",
          "key": "name",
          "sorter": [Function],
          "title": "Name",
        },
        Object {
          "className": "header-item",
          "dataIndex": "latency",
          "key": "latency",
          "render": [Function],
          "sorter": [Function],
          "title": "P95 Latency",
        },
        Object {
          "className": "header-item",
          "dataIndex": "requests",
          "key": "requests",
          "render": [Function],
          "sorter": [Function],
          "title": "Request rate",
        },
        Object {
          "className": "header-item",
          "dataIndex": "errRates",
          "key": "errRates",
          "render": [Function],
          "sorter": [Function],
          "title": "Error rate",
        },
        Object {
          "className": "header-item",
          "dataIndex": "impact",
          "defaultSortOrder": "descend",
          "key": "impact",
          "render": [Function],
          "sorter": [Function],
          "title": <div
            style={
              Object {
                "paddingTop": 1,
              }
            }
          >
            <span
              style={
                Object {
                  "color": "#459798",
                  "float": "left",
                }
              }
            >
              Impact
                
              <Tooltip
                classNames={
                  Object {
                    "root": "impact-tooltip",
                  }
                }
                placement="top"
                title="The result of multiplying avg. duration and requests per minute, showing the most used and slowest endpoints"
              >
                <IoInformationCircleOutline />
              </Tooltip>
            </span>
          </div>,
        },
      ]
    }
    onChange={[Function]}
    onRow={[Function]}
    pagination={
      Object {
        "defaultPageSize": 20,
        "pageSizeOptions": Array [
          "20",
          "50",
          "100",
        ],
        "showSizeChanger": true,
      }
    }
    rowClassName={[Function]}
  />
</Col>
`;

exports[`<OperationTableDetails> render No data table 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table ant-table-empty css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-placeholder"
                    style={
                      Object {
                        "display": null,
                      }
                    }
                  >
                    <td
                      className="ant-table-cell"
                      colSpan={5}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="css-dev-only-do-not-override-18afz5u ant-empty ant-empty-normal"
                        style={Object {}}
                      >
                        <div
                          className="ant-empty-image"
                          style={Object {}}
                        >
                          <svg
                            height="41"
                            viewBox="0 0 64 41"
                            width="64"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <title>
                              No data
                            </title>
                            <g
                              fill="none"
                              fillRule="evenodd"
                              transform="translate(0 1)"
                            >
                              <ellipse
                                cx="32"
                                cy="33"
                                fill="#f5f5f5"
                                rx="32"
                                ry="7"
                              />
                              <g
                                fillRule="nonzero"
                                stroke="#d9d9d9"
                              >
                                <path
                                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                />
                                <path
                                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                  fill="#fafafa"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div
                          className="ant-empty-description"
                          style={Object {}}
                        >
                          No data
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<OperationTableDetails> render P95 latency with more than 2 decimal places value 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-row ant-table-row-level-0 table-row"
                    data-row-key={0}
                    onClick={[Function]}
                    onMouseEnter={[Function]}
                    onMouseLeave={[Function]}
                    style={Object {}}
                  >
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      /PlaceOrder
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts55-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,3.369834710743838L100,14L0,14Z"
                                    fill="#869ADD"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,3.369834710743838"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#869ADD"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          298.8μs
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts58-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,11.666666666666668L100,11.666666666666668L100,14L0,14Z"
                                    fill="#4795BA"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,11.666666666666668L100,11.666666666666668"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#4795BA"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          &lt; 0.1 req/s
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts61-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,0L100,14L0,14Z"
                                    fill="#CD513A"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,0"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#CD513A"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          100%
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item ant-table-column-sort"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          aria-valuemax={100}
                          aria-valuemin={0}
                          aria-valuenow={100}
                          className="ant-progress ant-progress-status-success ant-progress-line ant-progress-line-align-end ant-progress-line-position-outer ant-progress-default impact css-dev-only-do-not-override-18afz5u"
                          role="progressbar"
                          style={Object {}}
                        >
                          <div
                            className="ant-progress-outer"
                            style={
                              Object {
                                "width": "100%",
                              }
                            }
                          >
                            <div
                              className="ant-progress-inner"
                              style={
                                Object {
                                  "backgroundColor": undefined,
                                  "borderRadius": 0,
                                }
                              }
                            >
                              <div
                                className="ant-progress-bg ant-progress-bg-outer"
                                style={
                                  Object {
                                    "--progress-line-stroke-color": "#459798",
                                    "--progress-percent": 1,
                                    "background": "#459798",
                                    "borderRadius": 0,
                                    "height": 8,
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          className="view-trace-button"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ul
          className="ant-pagination ant-table-pagination ant-table-pagination-right css-dev-only-do-not-override-18afz5u"
          style={Object {}}
        >
          <li
            aria-disabled={true}
            className="ant-pagination-prev ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Previous Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="left"
                className="anticon anticon-left"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="left"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-left"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                    key="svg-left-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={0}
            title="1"
          >
            <a
              rel="nofollow"
            >
              1
            </a>
          </li>
          <li
            aria-disabled={true}
            className="ant-pagination-next ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Next Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="right"
                className="anticon anticon-right"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="right"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-right"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    key="svg-right-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-options"
          >
            <div
              aria-label="Page Size"
              className="ant-select ant-select-outlined ant-pagination-options-size-changer css-dev-only-do-not-override-18afz5u ant-select-single ant-select-show-arrow ant-select-show-search"
              onBlur={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              onMouseDown={[Function]}
              style={Object {}}
            >
              Array [
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-wrap"
                  >
                    <span
                      className="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded={false}
                        aria-haspopup="listbox"
                        aria-label="Page Size"
                        aria-owns="undefined_list"
                        autoComplete="off"
                        className="ant-select-selection-search-input"
                        disabled={false}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionStart={[Function]}
                        onKeyDown={[Function]}
                        onMouseDown={[Function]}
                        onPaste={[Function]}
                        readOnly={false}
                        role="combobox"
                        style={
                          Object {
                            "opacity": null,
                          }
                        }
                        type="search"
                        unselectable={null}
                        value=""
                      />
                    </span>
                    <span
                      className="ant-select-selection-item"
                      title="20 / page"
                    >
                      20 / page
                    </span>
                  </span>
                </div>,
                "",
              ]
              <span
                aria-hidden={true}
                className="ant-select-arrow"
                onMouseDown={[Function]}
                style={
                  Object {
                    "WebkitUserSelect": "none",
                    "userSelect": "none",
                  }
                }
                unselectable="on"
              >
                <span
                  aria-label="down"
                  className="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    key="svg-down"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      key="svg-down-svg-0"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

exports[`<OperationTableDetails> render error rate with more than 2 decimal places value 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-row ant-table-row-level-0 table-row"
                    data-row-key={0}
                    onClick={[Function]}
                    onMouseEnter={[Function]}
                    onMouseLeave={[Function]}
                    style={Object {}}
                  >
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      /PlaceOrder
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts37-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,3.369834710743838L100,14L0,14Z"
                                    fill="#869ADD"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,3.369834710743838"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#869ADD"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          33.33ms
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts40-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,11.666666666666668L100,11.666666666666668L100,14L0,14Z"
                                    fill="#4795BA"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,11.666666666666668L100,11.666666666666668"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#4795BA"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          &lt; 0.1 req/s
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts43-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,0L100,14L0,14Z"
                                    fill="#CD513A"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,0"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#CD513A"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          100%
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item ant-table-column-sort"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          aria-valuemax={100}
                          aria-valuemin={0}
                          aria-valuenow={100}
                          className="ant-progress ant-progress-status-success ant-progress-line ant-progress-line-align-end ant-progress-line-position-outer ant-progress-default impact css-dev-only-do-not-override-18afz5u"
                          role="progressbar"
                          style={Object {}}
                        >
                          <div
                            className="ant-progress-outer"
                            style={
                              Object {
                                "width": "100%",
                              }
                            }
                          >
                            <div
                              className="ant-progress-inner"
                              style={
                                Object {
                                  "backgroundColor": undefined,
                                  "borderRadius": 0,
                                }
                              }
                            >
                              <div
                                className="ant-progress-bg ant-progress-bg-outer"
                                style={
                                  Object {
                                    "--progress-line-stroke-color": "#459798",
                                    "--progress-percent": 1,
                                    "background": "#459798",
                                    "borderRadius": 0,
                                    "height": 8,
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          className="view-trace-button"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ul
          className="ant-pagination ant-table-pagination ant-table-pagination-right css-dev-only-do-not-override-18afz5u"
          style={Object {}}
        >
          <li
            aria-disabled={true}
            className="ant-pagination-prev ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Previous Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="left"
                className="anticon anticon-left"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="left"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-left"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                    key="svg-left-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={0}
            title="1"
          >
            <a
              rel="nofollow"
            >
              1
            </a>
          </li>
          <li
            aria-disabled={true}
            className="ant-pagination-next ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Next Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="right"
                className="anticon anticon-right"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="right"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-right"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    key="svg-right-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-options"
          >
            <div
              aria-label="Page Size"
              className="ant-select ant-select-outlined ant-pagination-options-size-changer css-dev-only-do-not-override-18afz5u ant-select-single ant-select-show-arrow ant-select-show-search"
              onBlur={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              onMouseDown={[Function]}
              style={Object {}}
            >
              Array [
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-wrap"
                  >
                    <span
                      className="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded={false}
                        aria-haspopup="listbox"
                        aria-label="Page Size"
                        aria-owns="undefined_list"
                        autoComplete="off"
                        className="ant-select-selection-search-input"
                        disabled={false}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionStart={[Function]}
                        onKeyDown={[Function]}
                        onMouseDown={[Function]}
                        onPaste={[Function]}
                        readOnly={false}
                        role="combobox"
                        style={
                          Object {
                            "opacity": null,
                          }
                        }
                        type="search"
                        unselectable={null}
                        value=""
                      />
                    </span>
                    <span
                      className="ant-select-selection-item"
                      title="20 / page"
                    >
                      20 / page
                    </span>
                  </span>
                </div>,
                "",
              ]
              <span
                aria-hidden={true}
                className="ant-select-arrow"
                onMouseDown={[Function]}
                style={
                  Object {
                    "WebkitUserSelect": "none",
                    "userSelect": "none",
                  }
                }
                unselectable="on"
              >
                <span
                  aria-label="down"
                  className="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    key="svg-down"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      key="svg-down-svg-0"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

exports[`<OperationTableDetails> render lower than 0.1 P95 latency 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-row ant-table-row-level-0 table-row"
                    data-row-key={0}
                    onClick={[Function]}
                    onMouseEnter={[Function]}
                    onMouseLeave={[Function]}
                    style={Object {}}
                  >
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      /PlaceOrder
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts46-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,3.369834710743838L100,14L0,14Z"
                                    fill="#869ADD"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,3.369834710743838"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#869ADD"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          &lt; 0.1μs
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts49-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,11.666666666666668L100,11.666666666666668L100,14L0,14Z"
                                    fill="#4795BA"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,11.666666666666668L100,11.666666666666668"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#4795BA"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          &lt; 0.1 req/s
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts52-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,0L100,14L0,14Z"
                                    fill="#CD513A"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,0"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#CD513A"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          100%
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item ant-table-column-sort"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          aria-valuemax={100}
                          aria-valuemin={0}
                          aria-valuenow={100}
                          className="ant-progress ant-progress-status-success ant-progress-line ant-progress-line-align-end ant-progress-line-position-outer ant-progress-default impact css-dev-only-do-not-override-18afz5u"
                          role="progressbar"
                          style={Object {}}
                        >
                          <div
                            className="ant-progress-outer"
                            style={
                              Object {
                                "width": "100%",
                              }
                            }
                          >
                            <div
                              className="ant-progress-inner"
                              style={
                                Object {
                                  "backgroundColor": undefined,
                                  "borderRadius": 0,
                                }
                              }
                            >
                              <div
                                className="ant-progress-bg ant-progress-bg-outer"
                                style={
                                  Object {
                                    "--progress-line-stroke-color": "#459798",
                                    "--progress-percent": 1,
                                    "background": "#459798",
                                    "borderRadius": 0,
                                    "height": 8,
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          className="view-trace-button"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ul
          className="ant-pagination ant-table-pagination ant-table-pagination-right css-dev-only-do-not-override-18afz5u"
          style={Object {}}
        >
          <li
            aria-disabled={true}
            className="ant-pagination-prev ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Previous Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="left"
                className="anticon anticon-left"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="left"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-left"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                    key="svg-left-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={0}
            title="1"
          >
            <a
              rel="nofollow"
            >
              1
            </a>
          </li>
          <li
            aria-disabled={true}
            className="ant-pagination-next ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Next Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="right"
                className="anticon anticon-right"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="right"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-right"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    key="svg-right-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-options"
          >
            <div
              aria-label="Page Size"
              className="ant-select ant-select-outlined ant-pagination-options-size-changer css-dev-only-do-not-override-18afz5u ant-select-single ant-select-show-arrow ant-select-show-search"
              onBlur={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              onMouseDown={[Function]}
              style={Object {}}
            >
              Array [
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-wrap"
                  >
                    <span
                      className="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded={false}
                        aria-haspopup="listbox"
                        aria-label="Page Size"
                        aria-owns="undefined_list"
                        autoComplete="off"
                        className="ant-select-selection-search-input"
                        disabled={false}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionStart={[Function]}
                        onKeyDown={[Function]}
                        onMouseDown={[Function]}
                        onPaste={[Function]}
                        readOnly={false}
                        role="combobox"
                        style={
                          Object {
                            "opacity": null,
                          }
                        }
                        type="search"
                        unselectable={null}
                        value=""
                      />
                    </span>
                    <span
                      className="ant-select-selection-item"
                      title="20 / page"
                    >
                      20 / page
                    </span>
                  </span>
                </div>,
                "",
              ]
              <span
                aria-hidden={true}
                className="ant-select-arrow"
                onMouseDown={[Function]}
                style={
                  Object {
                    "WebkitUserSelect": "none",
                    "userSelect": "none",
                  }
                }
                unselectable="on"
              >
                <span
                  aria-label="down"
                  className="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    key="svg-down"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      key="svg-down-svg-0"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

exports[`<OperationTableDetails> render lower than 0.1 error rate 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-row ant-table-row-level-0 table-row"
                    data-row-key={0}
                    onClick={[Function]}
                    onMouseEnter={[Function]}
                    onMouseLeave={[Function]}
                    style={Object {}}
                  >
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      /PlaceOrder
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts28-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,3.369834710743838L100,14L0,14Z"
                                    fill="#869ADD"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,3.369834710743838"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#869ADD"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          736.16ms
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts31-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,11.666666666666668L100,11.666666666666668L100,14L0,14Z"
                                    fill="#4795BA"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,11.666666666666668L100,11.666666666666668"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#4795BA"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          &lt; 0.1 req/s
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts34-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,0L100,14L0,14Z"
                                    fill="#CD513A"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,0"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#CD513A"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          &lt; 0.1%
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item ant-table-column-sort"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          aria-valuemax={100}
                          aria-valuemin={0}
                          aria-valuenow={100}
                          className="ant-progress ant-progress-status-success ant-progress-line ant-progress-line-align-end ant-progress-line-position-outer ant-progress-default impact css-dev-only-do-not-override-18afz5u"
                          role="progressbar"
                          style={Object {}}
                        >
                          <div
                            className="ant-progress-outer"
                            style={
                              Object {
                                "width": "100%",
                              }
                            }
                          >
                            <div
                              className="ant-progress-inner"
                              style={
                                Object {
                                  "backgroundColor": undefined,
                                  "borderRadius": 0,
                                }
                              }
                            >
                              <div
                                className="ant-progress-bg ant-progress-bg-outer"
                                style={
                                  Object {
                                    "--progress-line-stroke-color": "#459798",
                                    "--progress-percent": 1,
                                    "background": "#459798",
                                    "borderRadius": 0,
                                    "height": 8,
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          className="view-trace-button"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ul
          className="ant-pagination ant-table-pagination ant-table-pagination-right css-dev-only-do-not-override-18afz5u"
          style={Object {}}
        >
          <li
            aria-disabled={true}
            className="ant-pagination-prev ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Previous Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="left"
                className="anticon anticon-left"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="left"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-left"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                    key="svg-left-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={0}
            title="1"
          >
            <a
              rel="nofollow"
            >
              1
            </a>
          </li>
          <li
            aria-disabled={true}
            className="ant-pagination-next ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Next Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="right"
                className="anticon anticon-right"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="right"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-right"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    key="svg-right-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-options"
          >
            <div
              aria-label="Page Size"
              className="ant-select ant-select-outlined ant-pagination-options-size-changer css-dev-only-do-not-override-18afz5u ant-select-single ant-select-show-arrow ant-select-show-search"
              onBlur={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              onMouseDown={[Function]}
              style={Object {}}
            >
              Array [
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-wrap"
                  >
                    <span
                      className="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded={false}
                        aria-haspopup="listbox"
                        aria-label="Page Size"
                        aria-owns="undefined_list"
                        autoComplete="off"
                        className="ant-select-selection-search-input"
                        disabled={false}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionStart={[Function]}
                        onKeyDown={[Function]}
                        onMouseDown={[Function]}
                        onPaste={[Function]}
                        readOnly={false}
                        role="combobox"
                        style={
                          Object {
                            "opacity": null,
                          }
                        }
                        type="search"
                        unselectable={null}
                        value=""
                      />
                    </span>
                    <span
                      className="ant-select-selection-item"
                      title="20 / page"
                    >
                      20 / page
                    </span>
                  </span>
                </div>,
                "",
              ]
              <span
                aria-hidden={true}
                className="ant-select-arrow"
                onMouseDown={[Function]}
                style={
                  Object {
                    "WebkitUserSelect": "none",
                    "userSelect": "none",
                  }
                }
                unselectable="on"
              >
                <span
                  aria-label="down"
                  className="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    key="svg-down"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      key="svg-down-svg-0"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

exports[`<OperationTableDetails> render lower than 0.1 request rate value 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-row ant-table-row-level-0 table-row"
                    data-row-key={0}
                    onClick={[Function]}
                    onMouseEnter={[Function]}
                    onMouseLeave={[Function]}
                    style={Object {}}
                  >
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      /PlaceOrder
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts10-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,3.369834710743838L100,14L0,14Z"
                                    fill="#869ADD"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,3.369834710743838"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#869ADD"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          736.16ms
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts13-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,11.666666666666668L100,11.666666666666668L100,14L0,14Z"
                                    fill="#4795BA"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,11.666666666666668L100,11.666666666666668"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#4795BA"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          &lt; 0.1 req/s
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts16-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,0L100,14L0,14Z"
                                    fill="#CD513A"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,0"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#CD513A"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          100%
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item ant-table-column-sort"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          aria-valuemax={100}
                          aria-valuemin={0}
                          aria-valuenow={100}
                          className="ant-progress ant-progress-status-success ant-progress-line ant-progress-line-align-end ant-progress-line-position-outer ant-progress-default impact css-dev-only-do-not-override-18afz5u"
                          role="progressbar"
                          style={Object {}}
                        >
                          <div
                            className="ant-progress-outer"
                            style={
                              Object {
                                "width": "100%",
                              }
                            }
                          >
                            <div
                              className="ant-progress-inner"
                              style={
                                Object {
                                  "backgroundColor": undefined,
                                  "borderRadius": 0,
                                }
                              }
                            >
                              <div
                                className="ant-progress-bg ant-progress-bg-outer"
                                style={
                                  Object {
                                    "--progress-line-stroke-color": "#459798",
                                    "--progress-percent": 1,
                                    "background": "#459798",
                                    "borderRadius": 0,
                                    "height": 8,
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          className="view-trace-button"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ul
          className="ant-pagination ant-table-pagination ant-table-pagination-right css-dev-only-do-not-override-18afz5u"
          style={Object {}}
        >
          <li
            aria-disabled={true}
            className="ant-pagination-prev ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Previous Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="left"
                className="anticon anticon-left"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="left"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-left"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                    key="svg-left-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={0}
            title="1"
          >
            <a
              rel="nofollow"
            >
              1
            </a>
          </li>
          <li
            aria-disabled={true}
            className="ant-pagination-next ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Next Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="right"
                className="anticon anticon-right"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="right"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-right"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    key="svg-right-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-options"
          >
            <div
              aria-label="Page Size"
              className="ant-select ant-select-outlined ant-pagination-options-size-changer css-dev-only-do-not-override-18afz5u ant-select-single ant-select-show-arrow ant-select-show-search"
              onBlur={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              onMouseDown={[Function]}
              style={Object {}}
            >
              Array [
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-wrap"
                  >
                    <span
                      className="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded={false}
                        aria-haspopup="listbox"
                        aria-label="Page Size"
                        aria-owns="undefined_list"
                        autoComplete="off"
                        className="ant-select-selection-search-input"
                        disabled={false}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionStart={[Function]}
                        onKeyDown={[Function]}
                        onMouseDown={[Function]}
                        onPaste={[Function]}
                        readOnly={false}
                        role="combobox"
                        style={
                          Object {
                            "opacity": null,
                          }
                        }
                        type="search"
                        unselectable={null}
                        value=""
                      />
                    </span>
                    <span
                      className="ant-select-selection-item"
                      title="20 / page"
                    >
                      20 / page
                    </span>
                  </span>
                </div>,
                "",
              ]
              <span
                aria-hidden={true}
                className="ant-select-arrow"
                onMouseDown={[Function]}
                style={
                  Object {
                    "WebkitUserSelect": "none",
                    "userSelect": "none",
                  }
                }
                unselectable="on"
              >
                <span
                  aria-label="down"
                  className="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    key="svg-down"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      key="svg-down-svg-0"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

exports[`<OperationTableDetails> render request rate number with more than 2 decimal places value 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-row ant-table-row-level-0 table-row"
                    data-row-key={0}
                    onClick={[Function]}
                    onMouseEnter={[Function]}
                    onMouseLeave={[Function]}
                    style={Object {}}
                  >
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      /PlaceOrder
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts19-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,3.369834710743838L100,14L0,14Z"
                                    fill="#869ADD"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,3.369834710743838"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#869ADD"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          736.16ms
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts22-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,11.666666666666668L100,11.666666666666668L100,14L0,14Z"
                                    fill="#4795BA"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,11.666666666666668L100,11.666666666666668"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#4795BA"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          0.28 req/s
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts25-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,0L100,14L0,14Z"
                                    fill="#CD513A"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,0"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#CD513A"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          100%
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item ant-table-column-sort"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          aria-valuemax={100}
                          aria-valuemin={0}
                          aria-valuenow={100}
                          className="ant-progress ant-progress-status-success ant-progress-line ant-progress-line-align-end ant-progress-line-position-outer ant-progress-default impact css-dev-only-do-not-override-18afz5u"
                          role="progressbar"
                          style={Object {}}
                        >
                          <div
                            className="ant-progress-outer"
                            style={
                              Object {
                                "width": "100%",
                              }
                            }
                          >
                            <div
                              className="ant-progress-inner"
                              style={
                                Object {
                                  "backgroundColor": undefined,
                                  "borderRadius": 0,
                                }
                              }
                            >
                              <div
                                className="ant-progress-bg ant-progress-bg-outer"
                                style={
                                  Object {
                                    "--progress-line-stroke-color": "#459798",
                                    "--progress-percent": 1,
                                    "background": "#459798",
                                    "borderRadius": 0,
                                    "height": 8,
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          className="view-trace-button"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ul
          className="ant-pagination ant-table-pagination ant-table-pagination-right css-dev-only-do-not-override-18afz5u"
          style={Object {}}
        >
          <li
            aria-disabled={true}
            className="ant-pagination-prev ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Previous Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="left"
                className="anticon anticon-left"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="left"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-left"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                    key="svg-left-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={0}
            title="1"
          >
            <a
              rel="nofollow"
            >
              1
            </a>
          </li>
          <li
            aria-disabled={true}
            className="ant-pagination-next ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Next Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="right"
                className="anticon anticon-right"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="right"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-right"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    key="svg-right-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-options"
          >
            <div
              aria-label="Page Size"
              className="ant-select ant-select-outlined ant-pagination-options-size-changer css-dev-only-do-not-override-18afz5u ant-select-single ant-select-show-arrow ant-select-show-search"
              onBlur={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              onMouseDown={[Function]}
              style={Object {}}
            >
              Array [
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-wrap"
                  >
                    <span
                      className="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded={false}
                        aria-haspopup="listbox"
                        aria-label="Page Size"
                        aria-owns="undefined_list"
                        autoComplete="off"
                        className="ant-select-selection-search-input"
                        disabled={false}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionStart={[Function]}
                        onKeyDown={[Function]}
                        onMouseDown={[Function]}
                        onPaste={[Function]}
                        readOnly={false}
                        role="combobox"
                        style={
                          Object {
                            "opacity": null,
                          }
                        }
                        type="search"
                        unselectable={null}
                        value=""
                      />
                    </span>
                    <span
                      className="ant-select-selection-item"
                      title="20 / page"
                    >
                      20 / page
                    </span>
                  </span>
                </div>,
                "",
              ]
              <span
                aria-hidden={true}
                className="ant-select-arrow"
                onMouseDown={[Function]}
                style={
                  Object {
                    "WebkitUserSelect": "none",
                    "userSelect": "none",
                  }
                }
                unselectable="on"
              >
                <span
                  aria-label="down"
                  className="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    key="svg-down"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      key="svg-down-svg-0"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

exports[`<OperationTableDetails> render some values in the table 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-row ant-table-row-level-0 table-row"
                    data-row-key={0}
                    onClick={[Function]}
                    onMouseEnter={[Function]}
                    onMouseLeave={[Function]}
                    style={Object {}}
                  >
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      /PlaceOrder
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts1-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,3.369834710743838L100,14L0,14Z"
                                    fill="#869ADD"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,3.369834710743838"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#869ADD"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          736.16ms
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts4-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,11.666666666666668L100,11.666666666666668L100,14L0,14Z"
                                    fill="#4795BA"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,11.666666666666668L100,11.666666666666668"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#4795BA"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          &lt; 0.1 req/s
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-container"
                        >
                          <div
                            className="recharts-wrapper"
                            style={
                              Object {
                                "cursor": "default",
                                "height": 15,
                                "position": "relative",
                                "width": 100,
                              }
                            }
                          >
                            <svg
                              className="recharts-surface"
                              height={15}
                              style={
                                Object {
                                  "height": "100%",
                                  "width": "100%",
                                }
                              }
                              viewBox="0 0 100 15"
                              width={100}
                            >
                              <title />
                              <desc />
                              <defs>
                                <clipPath
                                  id="recharts7-clip"
                                >
                                  <rect
                                    height={14}
                                    width={100}
                                    x={0}
                                    y={0}
                                  />
                                </clipPath>
                              </defs>
                              <g
                                className="recharts-layer recharts-area"
                              >
                                <g
                                  className="recharts-layer"
                                  clipPath={null}
                                >
                                  <path
                                    className="recharts-curve recharts-area-area"
                                    d="M0,0L100,0L100,14L0,14Z"
                                    fill="#CD513A"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="none"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                  <path
                                    className="recharts-curve recharts-area-curve"
                                    d="M0,0L100,0"
                                    fill="none"
                                    fillOpacity={1}
                                    height={14}
                                    stroke="#CD513A"
                                    strokeWidth={1}
                                    width={100}
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                        <div
                          className="table-graph-avg"
                        >
                          100%
                        </div>
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item ant-table-column-sort"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          aria-valuemax={100}
                          aria-valuemin={0}
                          aria-valuenow={100}
                          className="ant-progress ant-progress-status-success ant-progress-line ant-progress-line-align-end ant-progress-line-position-outer ant-progress-default impact css-dev-only-do-not-override-18afz5u"
                          role="progressbar"
                          style={Object {}}
                        >
                          <div
                            className="ant-progress-outer"
                            style={
                              Object {
                                "width": "100%",
                              }
                            }
                          >
                            <div
                              className="ant-progress-inner"
                              style={
                                Object {
                                  "backgroundColor": undefined,
                                  "borderRadius": 0,
                                }
                              }
                            >
                              <div
                                className="ant-progress-bg ant-progress-bg-outer"
                                style={
                                  Object {
                                    "--progress-line-stroke-color": "#459798",
                                    "--progress-percent": 1,
                                    "background": "#459798",
                                    "borderRadius": 0,
                                    "height": 8,
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          className="view-trace-button"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ul
          className="ant-pagination ant-table-pagination ant-table-pagination-right css-dev-only-do-not-override-18afz5u"
          style={Object {}}
        >
          <li
            aria-disabled={true}
            className="ant-pagination-prev ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Previous Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="left"
                className="anticon anticon-left"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="left"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-left"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                    key="svg-left-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={0}
            title="1"
          >
            <a
              rel="nofollow"
            >
              1
            </a>
          </li>
          <li
            aria-disabled={true}
            className="ant-pagination-next ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Next Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="right"
                className="anticon anticon-right"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="right"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-right"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    key="svg-right-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-options"
          >
            <div
              aria-label="Page Size"
              className="ant-select ant-select-outlined ant-pagination-options-size-changer css-dev-only-do-not-override-18afz5u ant-select-single ant-select-show-arrow ant-select-show-search"
              onBlur={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              onMouseDown={[Function]}
              style={Object {}}
            >
              Array [
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-wrap"
                  >
                    <span
                      className="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded={false}
                        aria-haspopup="listbox"
                        aria-label="Page Size"
                        aria-owns="undefined_list"
                        autoComplete="off"
                        className="ant-select-selection-search-input"
                        disabled={false}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionStart={[Function]}
                        onKeyDown={[Function]}
                        onMouseDown={[Function]}
                        onPaste={[Function]}
                        readOnly={false}
                        role="combobox"
                        style={
                          Object {
                            "opacity": null,
                          }
                        }
                        type="search"
                        unselectable={null}
                        value=""
                      />
                    </span>
                    <span
                      className="ant-select-selection-item"
                      title="20 / page"
                    >
                      20 / page
                    </span>
                  </span>
                </div>,
                "",
              ]
              <span
                aria-hidden={true}
                className="ant-select-arrow"
                onMouseDown={[Function]}
                style={
                  Object {
                    "WebkitUserSelect": "none",
                    "userSelect": "none",
                  }
                }
                unselectable="on"
              >
                <span
                  aria-label="down"
                  className="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    key="svg-down"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      key="svg-down-svg-0"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

exports[`<OperationTableDetails> test column render function 1`] = `
<div
  className="ant-col ant-col-24 css-dev-only-do-not-override-18afz5u"
  style={Object {}}
>
  <div
    className="ant-table-wrapper css-dev-only-do-not-override-18afz5u"
    style={Object {}}
  >
    <div
      className="ant-spin-nested-loading css-dev-only-do-not-override-18afz5u"
    >
      <div
        className="ant-spin-container"
        key="container"
      >
        <div
          className="ant-table css-dev-only-do-not-override-18afz5u"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-content"
              onScroll={[Function]}
              style={Object {}}
            >
              <table
                style={
                  Object {
                    "tableLayout": "auto",
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      aria-label="Name"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Name
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="P95 Latency"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            P95 Latency
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Request rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Request rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label="Error rate"
                      className="ant-table-cell header-item ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            Error rate
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                    <th
                      aria-label=""
                      aria-sort="descending"
                      className="ant-table-cell header-item ant-table-column-sort ant-table-column-has-sorters"
                      colSpan={null}
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      scope="col"
                      style={Object {}}
                      tabIndex={0}
                    >
                      Array [
                        <div
                          aria-describedby="test-id"
                          className="ant-table-column-sorters"
                          onMouseEnter={[Function]}
                          onMouseLeave={[Function]}
                          onPointerEnter={[Function]}
                          onPointerLeave={[Function]}
                        >
                          <span
                            className="ant-table-column-title"
                          >
                            <div
                              style={
                                Object {
                                  "paddingTop": 1,
                                }
                              }
                            >
                              <span
                                style={
                                  Object {
                                    "color": "#459798",
                                    "float": "left",
                                  }
                                }
                              >
                                Impact
                                  
                                Array [
                                  <svg
                                    aria-describedby="test-id"
                                    fill="currentColor"
                                    height="1em"
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    onPointerEnter={[Function]}
                                    onPointerLeave={[Function]}
                                    stroke="currentColor"
                                    strokeWidth="0"
                                    style={
                                      Object {
                                        "color": undefined,
                                      }
                                    }
                                    viewBox="0 0 512 512"
                                    width="1em"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"
                                      fill="none"
                                      key="0"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M220 220h32v116"
                                      fill="none"
                                      key="1"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M208 340h88"
                                      fill="none"
                                      key="2"
                                      strokeLinecap="round"
                                      strokeMiterlimit="10"
                                      strokeWidth="32"
                                    />
                                    <path
                                      d="M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"
                                      key="3"
                                    />
                                  </svg>,
                                  "",
                                ]
                              </span>
                            </div>
                          </span>
                          <span
                            className="ant-table-column-sorter ant-table-column-sorter-full"
                          >
                            <span
                              aria-hidden="true"
                              className="ant-table-column-sorter-inner"
                            >
                              <span
                                aria-label="caret-up"
                                className="anticon anticon-caret-up ant-table-column-sorter-up"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-up"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-up"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                                    key="svg-caret-up-svg-0"
                                  />
                                </svg>
                              </span>
                              <span
                                aria-label="caret-down"
                                className="anticon anticon-caret-down ant-table-column-sorter-down active"
                                role="img"
                              >
                                <svg
                                  aria-hidden="true"
                                  data-icon="caret-down"
                                  fill="currentColor"
                                  focusable="false"
                                  height="1em"
                                  key="svg-caret-down"
                                  viewBox="0 0 1024 1024"
                                  width="1em"
                                >
                                  <path
                                    d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                                    key="svg-caret-down-svg-0"
                                  />
                                </svg>
                              </span>
                            </span>
                          </span>
                        </div>,
                        "",
                      ]
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    className="ant-table-row ant-table-row-level-0 table-row"
                    onClick={[Function]}
                    onMouseEnter={[Function]}
                    onMouseLeave={[Function]}
                    style={Object {}}
                  >
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    />
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-graph-placeholder"
                        >
                          No Data
                        </div>
                        <div
                          className="table-graph-avg"
                        />
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-graph-placeholder"
                        >
                          No Data
                        </div>
                        <div
                          className="table-graph-avg"
                        />
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          className="ops-graph-placeholder"
                        >
                          No Data
                        </div>
                        <div
                          className="table-graph-avg"
                        />
                      </div>
                    </td>
                    <td
                      className="ant-table-cell header-item ant-table-column-sort"
                      colSpan={null}
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                      rowSpan={null}
                      style={Object {}}
                    >
                      <div
                        className="column-container"
                      >
                        <div
                          aria-valuemax={100}
                          aria-valuemin={0}
                          aria-valuenow={NaN}
                          className="ant-progress ant-progress-status-normal ant-progress-line ant-progress-line-align-end ant-progress-line-position-outer ant-progress-default impact css-dev-only-do-not-override-18afz5u"
                          role="progressbar"
                          style={Object {}}
                        >
                          <div
                            className="ant-progress-outer"
                            style={
                              Object {
                                "width": "100%",
                              }
                            }
                          >
                            <div
                              className="ant-progress-inner"
                              style={
                                Object {
                                  "backgroundColor": undefined,
                                  "borderRadius": 0,
                                }
                              }
                            >
                              <div
                                className="ant-progress-bg ant-progress-bg-outer"
                                style={
                                  Object {
                                    "--progress-line-stroke-color": "#459798",
                                    "--progress-percent": 0,
                                    "background": "#459798",
                                    "borderRadius": 0,
                                    "height": 8,
                                    "width": "0%",
                                  }
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          className="view-trace-button"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ul
          className="ant-pagination ant-table-pagination ant-table-pagination-right css-dev-only-do-not-override-18afz5u"
          style={Object {}}
        >
          <li
            aria-disabled={true}
            className="ant-pagination-prev ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Previous Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="left"
                className="anticon anticon-left"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="left"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-left"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                    key="svg-left-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={0}
            title="1"
          >
            <a
              rel="nofollow"
            >
              1
            </a>
          </li>
          <li
            aria-disabled={true}
            className="ant-pagination-next ant-pagination-disabled"
            onClick={[Function]}
            onKeyDown={[Function]}
            tabIndex={null}
            title="Next Page"
          >
            <button
              className="ant-pagination-item-link"
              disabled={true}
              tabIndex={-1}
              type="button"
            >
              <span
                aria-label="right"
                className="anticon anticon-right"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="right"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  key="svg-right"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    key="svg-right-svg-0"
                  />
                </svg>
              </span>
            </button>
          </li>
          <li
            className="ant-pagination-options"
          >
            <div
              aria-label="Page Size"
              className="ant-select ant-select-outlined ant-pagination-options-size-changer css-dev-only-do-not-override-18afz5u ant-select-single ant-select-show-arrow ant-select-show-search"
              onBlur={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              onMouseDown={[Function]}
              style={Object {}}
            >
              Array [
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-wrap"
                  >
                    <span
                      className="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded={false}
                        aria-haspopup="listbox"
                        aria-label="Page Size"
                        aria-owns="undefined_list"
                        autoComplete="off"
                        className="ant-select-selection-search-input"
                        disabled={false}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionStart={[Function]}
                        onKeyDown={[Function]}
                        onMouseDown={[Function]}
                        onPaste={[Function]}
                        readOnly={false}
                        role="combobox"
                        style={
                          Object {
                            "opacity": null,
                          }
                        }
                        type="search"
                        unselectable={null}
                        value=""
                      />
                    </span>
                    <span
                      className="ant-select-selection-item"
                      title="20 / page"
                    >
                      20 / page
                    </span>
                  </span>
                </div>,
                "",
              ]
              <span
                aria-hidden={true}
                className="ant-select-arrow"
                onMouseDown={[Function]}
                style={
                  Object {
                    "WebkitUserSelect": "none",
                    "userSelect": "none",
                  }
                }
                unselectable="on"
              >
                <span
                  aria-label="down"
                  className="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    key="svg-down"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      key="svg-down-svg-0"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;
