// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<DdgNodeContent> getNodeRenderer() returns a <DdgNodeContent /> 1`] = `
Object {
  "focalNodeUrl": "/deep-dependencies?operation=the-operation&service=the-service",
  "focusPathsThroughVertex": undefined,
  "getGenerationVisibility": undefined,
  "getVisiblePathElems": undefined,
  "hideVertex": undefined,
  "isFocalNode": false,
  "isPositioned": false,
  "operation": "the-operation",
  "selectVertex": undefined,
  "service": "the-service",
  "setOperation": undefined,
  "setViewModifier": undefined,
  "updateGenerationVisibility": undefined,
  "vertex": Object {
    "isFocalNode": false,
    "key": "some-key",
    "operation": "the-operation",
    "service": "the-service",
  },
  "vertexKey": "some-key",
}
`;

exports[`<DdgNodeContent> getNode<PERSON>enderer() returns a focal <DdgNodeContent /> 1`] = `
Object {
  "focalNodeUrl": null,
  "focusPathsThroughVertex": undefined,
  "getGenerationVisibility": undefined,
  "getVisiblePathElems": undefined,
  "hideVertex": undefined,
  "isFocalNode": true,
  "isPositioned": false,
  "operation": "the-operation",
  "selectVertex": undefined,
  "service": "the-service",
  "setOperation": undefined,
  "setViewModifier": undefined,
  "updateGenerationVisibility": undefined,
  "vertex": Object {
    "isFocalNode": true,
    "key": "some-key",
    "operation": "the-operation",
    "service": "the-service",
  },
  "vertexKey": "some-key",
}
`;

exports[`<DdgNodeContent> omits the operation if it is null 1`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <BreakableText
          text="some-operation"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> omits the operation if it is null 2`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> renders correctly when decorationValue is a string 1`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <BreakableText
          text="some-operation"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> renders correctly when decorationValue is a string 2`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core is-decorated is-missingDecoration"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <BreakableText
          text="some-operation"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> renders correctly when given decorationProgressbar 1`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <BreakableText
          text="some-operation"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> renders correctly when given decorationProgressbar 2`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <span>
    Test progressbar
  </span>
  <div
    className="DdgNodeContent--core is-decorated"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.2)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <BreakableText
          text="some-operation"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> renders correctly when isFocalNode = true and focalNodeUrl = null 1`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <BreakableText
          text="some-operation"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> renders correctly when isFocalNode = true and focalNodeUrl = null 2`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core is-focalNode"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <BreakableText
          text="some-operation"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": undefined,
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": false,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": false,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": false,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> renders the number of operations if there are multiple 1`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <BreakableText
          text="some-operation"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;

exports[`<DdgNodeContent> renders the number of operations if there are multiple 2`] = `
<div
  className="DdgNodeContent"
  onMouseOut={[Function]}
  onMouseOver={[Function]}
>
  <div
    className="DdgNodeContent--core"
    onClick={[Function]}
    role="button"
    style={
      Object {
        "height": "100px",
        "transform": "translate(25px, 25px) scale(1.5)",
        "width": "100px",
      }
    }
  >
    <div
      className="DdgNodeContent--labelWrapper"
    >
      <h4
        className="DdgNodeContent--label"
        style={
          Object {
            "marginTop": "10px",
            "width": "20px",
          }
        }
      >
        <BreakableText
          text="some-service"
          wordRegexp={/\\\\W\\*\\\\w\\+\\\\W\\*/g}
        />
      </h4>
      <div
        className="DdgNodeContent--label"
        style={
          Object {
            "paddingTop": "5px",
            "width": "30px",
          }
        }
      >
        <Popover
          content={
            <FilteredList
              options={
                Array [
                  "op0",
                  "op1",
                  "op2",
                  "op3",
                ]
              }
              setValue={[Function]}
              value={null}
            />
          }
          placement="bottom"
          title="Select Operation to Filter Graph"
        >
          <span>
            4 Operations
          </span>
        </Popover>
      </div>
    </div>
  </div>
  <ActionsMenu
    className="DdgNodeContent--actionsWrapper"
    items={
      Array [
        Object {
          "href": "some-url",
          "icon": <svg
            className="DdgNode--SetFocusIcon"
            height="100"
            viewBox="0 0 100 100"
            width="100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M50.0001 -1L61.0557 22.1383H38.9444L50.0001 -1Z"
                fill="currentColor"
              />
              <path
                d="M49.9999 99L38.9443 75.8617L61.0556 75.8617L49.9999 99Z"
                fill="currentColor"
              />
              <path
                d="M100 49L76.8617 60.0556L76.8617 37.9444L100 49Z"
                fill="currentColor"
              />
              <path
                d="M1.57361e-06 49L23.1383 37.9444L23.1383 60.0556L1.57361e-06 49Z"
                fill="currentColor"
              />
            </g>
          </svg>,
          "id": "set-focus",
          "isVisible": true,
          "label": "Set focus",
          "onClick": [Function],
        },
        Object {
          "icon": <NewWindowIcon />,
          "id": "view-traces",
          "label": "View traces",
          "onClick": [Function],
        },
        Object {
          "icon": <IoLocate />,
          "id": "focus-paths",
          "isVisible": true,
          "label": "Focus paths through this node",
          "onClick": [Function],
        },
        Object {
          "icon": <IoEyeOff />,
          "id": "hide-node",
          "isVisible": true,
          "label": "Hide node",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-parents",
          "isVisible": false,
          "label": "View Parents",
          "onClick": [Function],
        },
        Object {
          "checkboxProps": undefined,
          "icon": null,
          "id": "view-children",
          "isVisible": false,
          "label": "View Children",
          "onClick": [Function],
        },
      ]
    }
  />
</div>
`;
