// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`getNodeRenderers vectorBorder returns circle with .is-findMatch,	with .is-hovered,	with .is-pathHovered,	and with .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-findMatch is-hovered is-pathHovered is-focalNode"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle with .is-findMatch,	with .is-hovered,	with .is-pathHovered,	and without .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-findMatch is-hovered is-pathHovered"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle with .is-findMatch,	with .is-hovered,	without .is-pathHovered,	and with .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-findMatch is-hovered is-focalNode"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle with .is-findMatch,	with .is-hovered,	without .is-pathHovered,	and without .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-findMatch is-hovered"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle with .is-findMatch,	without .is-hovered,	with .is-pathHovered,	and with .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-findMatch is-pathHovered is-focalNode"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle with .is-findMatch,	without .is-hovered,	with .is-pathHovered,	and without .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-findMatch is-pathHovered"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle with .is-findMatch,	without .is-hovered,	without .is-pathHovered,	and with .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-findMatch is-focalNode"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle with .is-findMatch,	without .is-hovered,	without .is-pathHovered,	and without .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-findMatch"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle without .is-findMatch,	with .is-hovered,	with .is-pathHovered,	and with .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-hovered is-pathHovered is-focalNode"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle without .is-findMatch,	with .is-hovered,	with .is-pathHovered,	and without .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-hovered is-pathHovered"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle without .is-findMatch,	with .is-hovered,	without .is-pathHovered,	and with .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-hovered is-focalNode"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle without .is-findMatch,	with .is-hovered,	without .is-pathHovered,	and without .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-hovered"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle without .is-findMatch,	without .is-hovered,	with .is-pathHovered,	and with .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-pathHovered is-focalNode"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle without .is-findMatch,	without .is-hovered,	with .is-pathHovered,	and without .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-pathHovered"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle without .is-findMatch,	without .is-hovered,	without .is-pathHovered,	and with .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder is-focalNode"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorBorder returns circle without .is-findMatch,	without .is-hovered,	without .is-pathHovered,	and without .is-focalNode 1`] = `
<circle
  className="DdgNode--VectorBorder"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;

exports[`getNodeRenderers vectorFindColorBand returns circle with correct size and className 1`] = `
<circle
  className="DdgNode--VectorFindEmphasis--colorBand"
  cx={50}
  cy={50}
  r={49}
  vectorEffect="non-scaling-stroke"
/>
`;
