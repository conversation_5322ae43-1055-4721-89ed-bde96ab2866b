// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SidePanel> render renders 1`] = `
<div
  className="Ddg--DetailsPanel"
  style={
    Object {
      "width": "30vw",
    }
  }
>
  <div
    className="Ddg--DetailsPanel--SvcOpHeader"
  >
    <BreakableText
      text="test svc"
    />
  </div>
  <div
    className="Ddg--DetailsPanel--DecorationHeader"
  >
    <span>
      Decorating test svc
    </span>
  </div>
  <span
    className="Ddg--DetailsPanel--errorMsg"
  >
    test decorationValue
  </span>
  <VerticalResizer
    max={0.8}
    min={0.1}
    onChange={[Function]}
    position={0.3}
    rightSide={true}
  />
</div>
`;

exports[`<SidePanel> render renders detailLink 1`] = `
<div
  className="Ddg--DetailsPanel"
  style={
    Object {
      "width": "30vw",
    }
  }
>
  <div
    className="Ddg--DetailsPanel--SvcOpHeader"
  >
    <BreakableText
      text="test svc"
    />
  </div>
  <div
    className="Ddg--DetailsPanel--DecorationHeader"
  >
    <span>
      Decorating test svc
    </span>
    <Tooltip
      arrowPointAtCenter={true}
      title="More Info"
    >
      <a
        className="Ddg--DetailsPanel--DetailLink"
        href="test details link"
        rel="noreferrer noopener"
        target="_blank"
      >
        <NewWindowIcon />
      </a>
    </Tooltip>
  </div>
  <span
    className="Ddg--DetailsPanel--errorMsg"
  >
    test decorationValue
  </span>
  <div
    className="Ddg--DetailsPanel--LoadingWrapper"
  >
    <LoadingIndicator
      className="Ddg--DetailsPanel--LoadingIndicator"
    />
  </div>
  <VerticalResizer
    max={0.8}
    min={0.1}
    onChange={[Function]}
    position={0.3}
    rightSide={true}
  />
</div>
`;

exports[`<SidePanel> render renders details 1`] = `
<div
  className="Ddg--DetailsPanel"
  style={
    Object {
      "width": "30vw",
    }
  }
>
  <div
    className="Ddg--DetailsPanel--SvcOpHeader"
  >
    <BreakableText
      text="test svc"
    />
  </div>
  <div
    className="Ddg--DetailsPanel--DecorationHeader"
  >
    <span>
      Decorating test svc
    </span>
  </div>
  <span
    className="Ddg--DetailsPanel--errorMsg"
  >
    test decorationValue
  </span>
  <DetailsCard
    className="Ddg--DetailsPanel--DetailsCard "
    details="details string"
    header="Details"
  />
  <VerticalResizer
    max={0.8}
    min={0.1}
    onChange={[Function]}
    position={0.3}
    rightSide={true}
  />
</div>
`;

exports[`<SidePanel> render renders details error 1`] = `
<div
  className="Ddg--DetailsPanel"
  style={
    Object {
      "width": "30vw",
    }
  }
>
  <div
    className="Ddg--DetailsPanel--SvcOpHeader"
  >
    <BreakableText
      text="test svc"
    />
  </div>
  <div
    className="Ddg--DetailsPanel--DecorationHeader"
  >
    <span>
      Decorating test svc
    </span>
  </div>
  <span
    className="Ddg--DetailsPanel--errorMsg"
  >
    test decorationValue
  </span>
  <DetailsCard
    className="Ddg--DetailsPanel--DetailsCard is-error"
    details="details error"
    header="Details"
  />
  <VerticalResizer
    max={0.8}
    min={0.1}
    onChange={[Function]}
    position={0.3}
    rightSide={true}
  />
</div>
`;

exports[`<SidePanel> render renders omitted array of operations 1`] = `
<div
  className="Ddg--DetailsPanel"
  style={
    Object {
      "width": "30vw",
    }
  }
>
  <div
    className="Ddg--DetailsPanel--SvcOpHeader"
  >
    <BreakableText
      text="test svc"
    />
  </div>
  <div
    className="Ddg--DetailsPanel--DecorationHeader"
  >
    <span>
      Decorating test svc
    </span>
  </div>
  <span
    className="Ddg--DetailsPanel--errorMsg"
  >
    test decorationValue
  </span>
  <VerticalResizer
    max={0.8}
    min={0.1}
    onChange={[Function]}
    position={0.3}
    rightSide={true}
  />
</div>
`;

exports[`<SidePanel> render renders while loading 1`] = `
<div
  className="Ddg--DetailsPanel"
  style={
    Object {
      "width": "30vw",
    }
  }
>
  <div
    className="Ddg--DetailsPanel--SvcOpHeader"
  >
    <BreakableText
      text="test svc"
    />
  </div>
  <div
    className="Ddg--DetailsPanel--DecorationHeader"
  >
    <span>
      Decorating test svc
    </span>
  </div>
  <span
    className="Ddg--DetailsPanel--errorMsg"
  >
    test decorationValue
  </span>
  <div
    className="Ddg--DetailsPanel--LoadingWrapper"
  >
    <LoadingIndicator
      className="Ddg--DetailsPanel--LoadingIndicator"
    />
  </div>
  <VerticalResizer
    max={0.8}
    min={0.1}
    onChange={[Function]}
    position={0.3}
    rightSide={true}
  />
</div>
`;

exports[`<SidePanel> render renders with operation 1`] = `
<div
  className="Ddg--DetailsPanel"
  style={
    Object {
      "width": "30vw",
    }
  }
>
  <div
    className="Ddg--DetailsPanel--SvcOpHeader"
  >
    <BreakableText
      text="test svc"
    />
    <BreakableText
      text="::test op"
    />
  </div>
  <div
    className="Ddg--DetailsPanel--DecorationHeader"
  >
    <span>
      Decorating test svc
    </span>
  </div>
  <span
    className="Ddg--DetailsPanel--errorMsg"
  >
    test decorationValue
  </span>
  <VerticalResizer
    max={0.8}
    min={0.1}
    onChange={[Function]}
    position={0.3}
    rightSide={true}
  />
</div>
`;

exports[`<SidePanel> render renders with progressbar 1`] = `
<div
  className="Ddg--DetailsPanel"
  style={
    Object {
      "width": "30vw",
    }
  }
>
  <div
    className="Ddg--DetailsPanel--SvcOpHeader"
  >
    <BreakableText
      text="test svc"
    />
  </div>
  <div
    className="Ddg--DetailsPanel--DecorationHeader"
  >
    <span>
      Decorating test svc
    </span>
  </div>
  <div
    className="Ddg--DetailsPanel--PercentCircleWrapper"
  >
    <div>
      stand-in progressbar
    </div>
  </div>
  <VerticalResizer
    max={0.8}
    min={0.1}
    onChange={[Function]}
    position={0.3}
    rightSide={true}
  />
</div>
`;
