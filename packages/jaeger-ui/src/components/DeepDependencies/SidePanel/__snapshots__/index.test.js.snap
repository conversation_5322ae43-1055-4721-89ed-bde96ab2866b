// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SidePanel> info button  opens info modal 1`] = `
Object {
  "content": <Table
    columns={
      Array [
        Object {
          "dataIndex": "acronym",
          "key": "acronym",
          "title": "Acronym",
        },
        Object {
          "dataIndex": "name",
          "key": "name",
          "title": "Name",
        },
      ]
    }
    dataSource={
      Array [
        Object {
          "acronym": "1st",
          "id": "first",
          "name": "First Decoration",
        },
        Object {
          "acronym": "TA",
          "id": "test ID",
          "name": "Decoration to test interactions",
        },
        Object {
          "acronym": "LO",
          "id": "last",
          "name": "The last one",
        },
      ]
    }
    rowKey={[Function]}
  />,
  "maskClosable": true,
  "title": "Decoration Options",
  "width": "60vw",
}
`;

exports[`<SidePanel> render ignores selectedVertex without selected decoration 1`] = `
<div
  className="Ddg--SidePanel"
>
  <div
    className="Ddg--SidePanel--Btns"
  >
    <button
      className="Ddg--SidePanel--closeBtn is-hidden"
      onClick={[Function]}
      type="button"
    >
      <IoExitOutline />
    </button>
    <div
      className="Ddg--SidePanel--DecorationBtns"
    >
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="first"
        onClick={[Function]}
        type="button"
      >
        1st
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="test ID"
        onClick={[Function]}
        type="button"
      >
        TA
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="last"
        onClick={[Function]}
        type="button"
      >
        LO
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn"
        key="clearBtn"
        onClick={[Function]}
        type="button"
      >
        Clear
      </button>
    </div>
    <button
      className="Ddg--SidePanel--infoBtn"
      onClick={[Function]}
      type="button"
    >
      <IoInformationCircleOutline />
    </button>
  </div>
  <div
    className="Ddg--SidePanel--Details "
  />
</div>
`;

exports[`<SidePanel> render renders config decorations with clear button 1`] = `
<div
  className="Ddg--SidePanel"
>
  <div
    className="Ddg--SidePanel--Btns"
  >
    <button
      className="Ddg--SidePanel--closeBtn is-hidden"
      onClick={[Function]}
      type="button"
    >
      <IoExitOutline />
    </button>
    <div
      className="Ddg--SidePanel--DecorationBtns"
    >
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="first"
        onClick={[Function]}
        type="button"
      >
        1st
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="test ID"
        onClick={[Function]}
        type="button"
      >
        TA
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="last"
        onClick={[Function]}
        type="button"
      >
        LO
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn"
        key="clearBtn"
        onClick={[Function]}
        type="button"
      >
        Clear
      </button>
    </div>
    <button
      className="Ddg--SidePanel--infoBtn"
      onClick={[Function]}
      type="button"
    >
      <IoInformationCircleOutline />
    </button>
  </div>
  <div
    className="Ddg--SidePanel--Details "
  />
</div>
`;

exports[`<SidePanel> render renders selected decoration 1`] = `
<div
  className="Ddg--SidePanel"
>
  <div
    className="Ddg--SidePanel--Btns"
  >
    <button
      className="Ddg--SidePanel--closeBtn is-hidden"
      onClick={[Function]}
      type="button"
    >
      <IoExitOutline />
    </button>
    <div
      className="Ddg--SidePanel--DecorationBtns"
    >
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="first"
        onClick={[Function]}
        type="button"
      >
        1st
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn is-selected"
        key="test ID"
        onClick={[Function]}
        type="button"
      >
        TA
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="last"
        onClick={[Function]}
        type="button"
      >
        LO
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn"
        key="clearBtn"
        onClick={[Function]}
        type="button"
      >
        Clear
      </button>
    </div>
    <button
      className="Ddg--SidePanel--infoBtn"
      onClick={[Function]}
      type="button"
    >
      <IoInformationCircleOutline />
    </button>
  </div>
  <div
    className="Ddg--SidePanel--Details "
  />
</div>
`;

exports[`<SidePanel> render renders sidePanel and closeBtn when vertex and decoration are both selected 1`] = `
<div
  className="Ddg--SidePanel"
>
  <div
    className="Ddg--SidePanel--Btns"
  >
    <button
      className="Ddg--SidePanel--closeBtn "
      onClick={[Function]}
      type="button"
    >
      <IoExitOutline />
    </button>
    <div
      className="Ddg--SidePanel--DecorationBtns"
    >
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="first"
        onClick={[Function]}
        type="button"
      >
        1st
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn is-selected"
        key="test ID"
        onClick={[Function]}
        type="button"
      >
        TA
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn "
        key="last"
        onClick={[Function]}
        type="button"
      >
        LO
      </button>
      <button
        className="Ddg--SidePanel--decorationBtn"
        key="clearBtn"
        onClick={[Function]}
        type="button"
      >
        Clear
      </button>
    </div>
    <button
      className="Ddg--SidePanel--infoBtn"
      onClick={[Function]}
      type="button"
    >
      <IoInformationCircleOutline />
    </button>
  </div>
  <div
    className="Ddg--SidePanel--Details .is-expanded"
  >
    <Connect(UnconnectedDetailsPanel)
      decorationSchema={
        Object {
          "acronym": "TA",
          "id": "test ID",
          "name": "Decoration to test interactions",
        }
      }
      operation="op"
      service="svc"
    />
  </div>
</div>
`;
